<?php
/**
 * NextNaked functions and definitions
 *
 * @link https://developer.wordpress.org/themes/basics/theme-functions/
 *
 * @package NextNaked
 */

if ( ! defined( 'NEXTNAKED_VERSION' ) ) {
	// Replace the version number of the theme on each release.
	define( 'NEXTNAKED_VERSION', '1.0.0' );
}

/**
 * Sets up theme defaults and registers support for various WordPress features.
 */
function nextnaked_setup() {
	/*
	 * Make theme available for translation.
	 * Translations can be filed in the /languages/ directory.
	 */
	load_theme_textdomain( 'nextnaked', get_template_directory() . '/languages' );

	// Add default posts and comments RSS feed links to head.
	add_theme_support( 'automatic-feed-links' );

	/*
	 * Let WordPress manage the document title.
	 * By adding theme support, we declare that this theme does not use a
	 * hard-coded <title> tag in the document head, and expect WordPress to
	 * provide it for us.
	 */
	add_theme_support( 'title-tag' );

	/*
	 * Enable support for Post Thumbnails on posts and pages.
	 *
	 * @link https://developer.wordpress.org/themes/functionality/featured-images-post-thumbnails/
	 */
	add_theme_support( 'post-thumbnails' );

	// This theme uses wp_nav_menu() in one location.
	register_nav_menus(
		array(
			'menu-1' => esc_html__( 'Primary', 'nextnaked' ),
		)
	);

	/*
	 * Switch default core markup for search form, comment form, and comments
	 * to output valid HTML5.
	 */
	add_theme_support(
		'html5',
		array(
			'search-form',
			'comment-form',
			'comment-list',
			'gallery',
			'caption',
			'style',
			'script',
		)
	);

	// Set up the WordPress core custom background feature.
	add_theme_support(
		'custom-background',
		apply_filters(
			'nextnaked_custom_background_args',
			array(
				'default-color' => 'ffffff',
				'default-image' => '',
			)
		)
	);

	// Add theme support for selective refresh for widgets.
	add_theme_support( 'customize-selective-refresh-widgets' );

	/**
	 * Add support for core custom logo.
	 *
	 * @link https://codex.wordpress.org/Theme_Logo
	 */
	add_theme_support(
		'custom-logo',
		array(
			'height'      => 250,
			'width'       => 250,
			'flex-width'  => true,
			'flex-height' => true,
		)
	);

	// Add support for full and wide align images.
	add_theme_support( 'align-wide' );

	// Add support for responsive embeds.
	add_theme_support( 'responsive-embeds' );

	// Add support for editor styles.
	add_theme_support( 'editor-styles' );

	// Add support for Block Patterns.
	add_theme_support( 'block-patterns' );

	// Add support for Block Styles.
	add_theme_support( 'wp-block-styles' );

	// Add support for editor styles.
	add_theme_support( 'editor-styles' );
	add_editor_style( 'assets/css/editor-style.css' );

	// Add support for custom header.
	add_theme_support(
		'custom-header',
		apply_filters(
			'nextnaked_custom_header_args',
			array(
				'default-image'      => '',
				'default-text-color' => '000000',
				'width'              => 1600,
				'height'             => 250,
				'flex-height'        => true,
				'wp-head-callback'   => 'nextnaked_header_style',
			)
		)
	);
}
add_action( 'after_setup_theme', 'nextnaked_setup' );

/**
 * Set the content width in pixels, based on the theme's design and stylesheet.
 *
 * Priority 0 to make it available to lower priority callbacks.
 *
 * @global int $content_width
 */
function nextnaked_content_width() {
	$GLOBALS['content_width'] = apply_filters( 'nextnaked_content_width', 1200 );
}
add_action( 'after_setup_theme', 'nextnaked_content_width', 0 );

/**
 * Register widget area.
 *
 * @link https://developer.wordpress.org/themes/functionality/sidebars/#registering-a-sidebar
 */
function nextnaked_widgets_init() {
	register_sidebar(
		array(
			'name'          => esc_html__( 'Sidebar', 'nextnaked' ),
			'id'            => 'sidebar-1',
			'description'   => esc_html__( 'Add widgets here.', 'nextnaked' ),
			'before_widget' => '<section id="%1$s" class="widget %2$s">',
			'after_widget'  => '</section>',
			'before_title'  => '<h2 class="widget-title">',
			'after_title'   => '</h2>',
		)
	);
}
add_action( 'widgets_init', 'nextnaked_widgets_init' );

/**
 * Enqueue scripts and styles.
 */
function nextnaked_scripts() {
	// Add favicon
	add_action('wp_head', function() {
		echo '<link rel="icon" href="' . get_stylesheet_directory_uri() . '/assets/img/images/pacitto.webp">';
	});

	// Google Fonts - Rajdhani con preconnect per migliorare le prestazioni
	add_action('wp_head', function() {
		echo '<link rel="preconnect" href="https://fonts.googleapis.com">';
		echo '<link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>';
	});
	wp_enqueue_style( 'nextnaked-google-fonts', 'https://fonts.googleapis.com/css2?family=Rajdhani:wght@300;400;500;600;700&display=swap', array(), NEXTNAKED_VERSION );

	// Bootstrap CSS
	wp_enqueue_style( 'nextnaked-bootstrap', get_stylesheet_directory_uri() . '/assets/css/bootstrap.min.css', array(), NEXTNAKED_VERSION );

	// Font Awesome CSS
	wp_enqueue_style( 'nextnaked-font-awesome', get_stylesheet_directory_uri() . '/assets/css/font-awesome.min.css', array(), NEXTNAKED_VERSION );

	// Flaticon CSS
	wp_enqueue_style( 'nextnaked-flaticon', get_stylesheet_directory_uri() . '/assets/css/flaticon.min.css', array(), NEXTNAKED_VERSION );

	// Slick Slider CSS
	wp_enqueue_style( 'nextnaked-slick', get_stylesheet_directory_uri() . '/assets/css/plugins/slick.min.css', array(), NEXTNAKED_VERSION );

	// CSS Animation CSS
	wp_enqueue_style( 'nextnaked-cssanimation', get_stylesheet_directory_uri() . '/assets/css/plugins/cssanimation.min.css', array(), NEXTNAKED_VERSION );

	// Justified Gallery CSS
	wp_enqueue_style( 'nextnaked-justified-gallery', get_stylesheet_directory_uri() . '/assets/css/plugins/justifiedGallery.min.css', array(), NEXTNAKED_VERSION );

	// Light Gallery CSS
	wp_enqueue_style( 'nextnaked-light-gallery', get_stylesheet_directory_uri() . '/assets/css/plugins/light-gallery.min.css', array(), NEXTNAKED_VERSION );

	// AOS CSS
	wp_enqueue_style( 'nextnaked-aos', 'https://unpkg.com/aos@2.3.1/dist/aos.css', array(), '2.3.1' );

	// Main stylesheet
	wp_enqueue_style( 'nextnaked-style', get_stylesheet_directory_uri() . '/assets/css/main.css', array(), NEXTNAKED_VERSION );

	// Theme stylesheet (only for theme info)
	wp_enqueue_style( 'nextnaked-theme-info', get_stylesheet_uri(), array(), NEXTNAKED_VERSION );

	// Custom CSS
	wp_enqueue_style( 'nextnaked-custom', get_stylesheet_directory_uri() . '/assets/css/custom.css', array(), NEXTNAKED_VERSION );

	// Modernizr JS
	wp_enqueue_script( 'nextnaked-modernizr', get_template_directory_uri() . '/assets/js/modernizr-2.8.3.min.js', array(), '2.8.3', false );

	// jQuery is already included by WordPress

	// Popper JS
	wp_enqueue_script( 'nextnaked-popper', get_template_directory_uri() . '/assets/js/popper.min.js', array('jquery'), NEXTNAKED_VERSION, true );

	// Bootstrap JS
	wp_enqueue_script( 'nextnaked-bootstrap', get_template_directory_uri() . '/assets/js/bootstrap.min.js', array('jquery', 'nextnaked-popper'), NEXTNAKED_VERSION, true );

	// Slick slider JS
	wp_enqueue_script( 'nextnaked-slick', get_template_directory_uri() . '/assets/js/plugins/slick.min.js', array('jquery'), NEXTNAKED_VERSION, true );

	// Counterup JS
	wp_enqueue_script( 'nextnaked-counterup', get_template_directory_uri() . '/assets/js/plugins/counterup.min.js', array('jquery'), NEXTNAKED_VERSION, true );

	// Waypoint JS
	wp_enqueue_script( 'nextnaked-waypoint', get_template_directory_uri() . '/assets/js/plugins/waypoint.min.js', array('jquery'), NEXTNAKED_VERSION, true );

	// Justified Gallery JS
	wp_enqueue_script( 'nextnaked-justified-gallery', get_template_directory_uri() . '/assets/js/plugins/justifiedGallery.min.js', array('jquery'), NEXTNAKED_VERSION, true );

	// Image Loaded JS
	wp_enqueue_script( 'nextnaked-imageloaded', get_template_directory_uri() . '/assets/js/plugins/imageloaded.min.js', array('jquery'), NEXTNAKED_VERSION, true );

	// Masonry JS
	wp_enqueue_script( 'nextnaked-masonry', get_template_directory_uri() . '/assets/js/plugins/masonry.min.js', array('jquery', 'nextnaked-imageloaded'), NEXTNAKED_VERSION, true );

	// Light Gallery JS
	wp_enqueue_script( 'nextnaked-light-gallery', get_template_directory_uri() . '/assets/js/plugins/light-gallery.min.js', array('jquery'), NEXTNAKED_VERSION, true );

	// Mailchimp JS
	wp_enqueue_script( 'nextnaked-mailchimp', get_template_directory_uri() . '/assets/js/plugins/mailchimp-ajax-submit.min.js', array('jquery'), NEXTNAKED_VERSION, true );

	// AOS JS
	wp_enqueue_script( 'nextnaked-aos', 'https://unpkg.com/aos@2.3.1/dist/aos.js', array('jquery'), '2.3.1', true );

	// Main JavaScript file with defer attribute
	wp_enqueue_script( 'nextnaked-main', get_template_directory_uri() . '/assets/js/main.js', array('jquery', 'nextnaked-aos'), NEXTNAKED_VERSION, true );

	if ( is_singular() && comments_open() && get_option( 'thread_comments' ) ) {
		wp_enqueue_script( 'comment-reply' );
	}
}
add_action( 'wp_enqueue_scripts', 'nextnaked_scripts' );

/**
 * Custom template tags for this theme.
 */
require get_template_directory() . '/inc/template-tags.php';

/**
 * Functions which enhance the theme by hooking into WordPress.
 */
require get_template_directory() . '/inc/template-functions.php';

/**
 * Customizer additions.
 */
require get_template_directory() . '/inc/customizer.php';

/**
 * Load Jetpack compatibility file.
 */
if ( defined( 'JETPACK__VERSION' ) ) {
	require get_template_directory() . '/inc/jetpack.php';
}

/**
 * Disable WordPress emoji script for better performance
 */
function nextnaked_disable_emojis() {
	remove_action( 'wp_head', 'print_emoji_detection_script', 7 );
	remove_action( 'admin_print_scripts', 'print_emoji_detection_script' );
	remove_action( 'wp_print_styles', 'print_emoji_styles' );
	remove_action( 'admin_print_styles', 'print_emoji_styles' );
	remove_filter( 'the_content_feed', 'wp_staticize_emoji' );
	remove_filter( 'comment_text_rss', 'wp_staticize_emoji' );
	remove_filter( 'wp_mail', 'wp_staticize_emoji_for_email' );
	add_filter( 'tiny_mce_plugins', 'nextnaked_disable_emojis_tinymce' );
	add_filter( 'wp_resource_hints', 'nextnaked_disable_emojis_remove_dns_prefetch', 10, 2 );
}
add_action( 'init', 'nextnaked_disable_emojis' );

/**
 * Filter function used to remove the tinymce emoji plugin.
 */
function nextnaked_disable_emojis_tinymce( $plugins ) {
	if ( is_array( $plugins ) ) {
		return array_diff( $plugins, array( 'wpemoji' ) );
	}
	return array();
}

/**
 * Remove emoji CDN hostname from DNS prefetching hints.
 */
function nextnaked_disable_emojis_remove_dns_prefetch( $urls, $relation_type ) {
	if ( 'dns-prefetch' === $relation_type ) {
		$emoji_svg_url = apply_filters( 'emoji_svg_url', 'https://s.w.org/images/core/emoji/13.0.1/svg/' );
		$urls = array_diff( $urls, array( $emoji_svg_url ) );
	}
	return $urls;
}

/**
 * Add preload for critical assets
 */
function nextnaked_preload_assets() {
	// Preload solo gli asset veramente critici per il rendering iniziale
	// Preload critical CSS - solo quelli essenziali
	echo '<link rel="preload" href="' . get_stylesheet_directory_uri() . '/assets/css/bootstrap.min.css" as="style" onload="this.onload=null;this.rel=\'stylesheet\'">';
	echo '<link rel="preload" href="' . get_stylesheet_directory_uri() . '/assets/css/main.css" as="style" onload="this.onload=null;this.rel=\'stylesheet\'">';

	// Non precarica i font Google - verranno caricati normalmente
	// Non precarica modernizr - viene già caricato in modo sincrono

	// Aggiungi stile critico inline per prevenire FOUC
	echo '<style id="critical-css">
		.header-area, .mobile-header {
			opacity: 0;
			transition: opacity 0.5s ease-in-out;
		}
		.header-area.visible, .mobile-header.visible {
			opacity: 1;
		}
	</style>';
}
add_action( 'wp_head', 'nextnaked_preload_assets', 1 );

/**
 * Add defer attribute to non-critical scripts
 */
function nextnaked_defer_scripts( $tag, $handle, $src ) {
	// Add script handles to the array below that you want to defer
	$defer_scripts = array(
		'nextnaked-main',
		'nextnaked-popper',
		'nextnaked-bootstrap',
		'nextnaked-slick',
		'nextnaked-counterup',
		'nextnaked-waypoint',
		'nextnaked-justified-gallery',
		'nextnaked-imageloaded',
		'nextnaked-masonry',
		'nextnaked-light-gallery',
		'nextnaked-mailchimp',
		'nextnaked-aos'
	);

	if ( in_array( $handle, $defer_scripts ) ) {
		return str_replace( ' src', ' defer src', $tag );
	}

	return $tag;
}
add_filter( 'script_loader_tag', 'nextnaked_defer_scripts', 10, 3 );

/**
 * Styles the header image and text displayed on the blog.
 *
 * @see nextnaked_custom_header_setup().
 */
function nextnaked_header_style() {
	$header_text_color = get_header_textcolor();

	/*
	 * If no custom options for text are set, let's bail.
	 * get_header_textcolor() options: Any hex value, 'blank' to hide text. Default: add_theme_support( 'custom-header' ).
	 */
	if ( get_theme_support( 'custom-header', 'default-text-color' ) === $header_text_color ) {
		return;
	}

	// If we get this far, we have custom styles. Let's do this.
	?>
	<style type="text/css">
	<?php
	// Has the text been hidden?
	if ( ! display_header_text() ) :
		?>
		.site-title,
		.site-description {
			position: absolute;
			clip: rect(1px, 1px, 1px, 1px);
			}
		<?php
		// If the user has set a custom color for the text use that.
		else :
			?>
		.site-title a,
		.site-description {
			color: #<?php echo esc_attr( $header_text_color ); ?>;
		}
	<?php endif; ?>
	</style>
	<?php
}

/**
 * Register block patterns.
 */
function nextnaked_register_block_patterns() {
	if ( function_exists( 'register_block_pattern' ) ) {
		// Register a simple two column pattern.
		register_block_pattern(
			'nextnaked/two-columns',
			array(
				'title'       => __( 'Two columns content', 'nextnaked' ),
				'description' => __( 'Two columns with heading and text.', 'nextnaked' ),
				'categories'  => array( 'columns' ),
				'content'     => '<!-- wp:columns -->
<div class="wp-block-columns"><!-- wp:column -->
<div class="wp-block-column"><!-- wp:heading {"level":3} -->
<h3>' . __( 'Left Column', 'nextnaked' ) . '</h3>
<!-- /wp:heading -->

<!-- wp:paragraph -->
<p>' . __( 'This is an example of a column pattern that you can use in your content.', 'nextnaked' ) . '</p>
<!-- /wp:paragraph --></div>
<!-- /wp:column -->

<!-- wp:column -->
<div class="wp-block-column"><!-- wp:heading {"level":3} -->
<h3>' . __( 'Right Column', 'nextnaked' ) . '</h3>
<!-- /wp:heading -->

<!-- wp:paragraph -->
<p>' . __( 'Add any content you want here. You can modify this pattern to create your own.', 'nextnaked' ) . '</p>
<!-- /wp:paragraph --></div>
<!-- /wp:column --></div>
<!-- /wp:columns -->',
			)
		);
	}
}
add_action( 'init', 'nextnaked_register_block_patterns' );

/**
 * Register block styles.
 */
function nextnaked_register_block_styles() {
	if ( function_exists( 'register_block_style' ) ) {
		// Register a custom button style.
		register_block_style(
			'core/button',
			array(
				'name'  => 'nextnaked-button-rounded',
				'label' => __( 'Rounded', 'nextnaked' ),
			)
		);
	}
}
add_action( 'init', 'nextnaked_register_block_styles' );

/**
 * Fix Contact Form 7 submit button text
 */
function nextnaked_fix_cf7_submit_text() {
    add_filter('wpcf7_form_elements', function($content) {
        $content = str_replace('value="sens"', 'value="Invia messaggio"', $content);
        $content = str_replace('value="SEND"', 'value="Invia messaggio"', $content);
        $content = str_replace('value="Send"', 'value="Invia messaggio"', $content);
        return $content;
    });
}
add_action('init', 'nextnaked_fix_cf7_submit_text');

/**
 * Carica il CSS per le pagine delle policy (Privacy e Cookie)
 */
function nextnaked_policy_pages_styles() {
    if (is_page_template('Pagine/privacy-policy.php') || is_page_template('Pagine/cookie.php')) {
        wp_enqueue_style('policy-pages-style', get_template_directory_uri() . '/assets/css/policy-pages.css');
    }
}
add_action('wp_enqueue_scripts', 'nextnaked_policy_pages_styles');

/**
 * Carica il CSS per la pagina 404
 */
function nextnaked_404_page_styles() {
    if (is_404()) {
        wp_enqueue_style('404-page-style', get_template_directory_uri() . '/assets/css/404-page.css');
    }
}
add_action('wp_enqueue_scripts', 'nextnaked_404_page_styles');
