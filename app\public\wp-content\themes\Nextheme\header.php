<?php
/**
 * The header for our theme
 *
 * This is the template that displays all of the <head> section and everything up until <div id="content">
 *
 * @link https://developer.wordpress.org/themes/basics/template-files/#template-partials
 *
 * @package NextNaked
 */

?>
<!doctype html>
<html <?php language_attributes(); ?>>
<head>
	<meta charset="<?php bloginfo( 'charset' ); ?>">
	<meta name="viewport" content="width=device-width, initial-scale=1">
	<link rel="profile" href="https://gmpg.org/xfn/11">

	<?php wp_head(); ?>
</head>

<body <?php body_class(); ?>>
<?php wp_body_open(); ?>
<div id="page" class="site">
	<a class="skip-link screen-reader-text" href="#primary"><?php esc_html_e( 'Skip to content', 'nextnaked' ); ?></a>

	<!--====================  header area ====================-->
    <div class="header-area header-sticky bg-img space__inner--y40 background-repeat--x background-color--dark d-none d-lg-block" data-bg="<?php echo esc_url(get_template_directory_uri()); ?>/assets/img/icons/ruler.webp">
        <!-- header top -->
        <div class="header-top">
            <div class="container">
                <div class="row">
                    <div class="col-lg-4">
                        <div class="header-top-info">
                            <span class="header-top-info__image pr-1"><img width="20" height="19" src="<?php echo esc_url(get_template_directory_uri()); ?>/assets/img/icons/phone.webp" alt="Icona telefono"></span>
                            <span class="header-top-info__text"><?php echo esc_html(' 0775 408250'); ?></span>
                        </div>
                    </div>
                    <div class="col-lg-4">
                        <div class="header-top-info text-center">
                            <span class="header-top-info__image pr-1"><img width="19" height="19" src="<?php echo esc_url(get_template_directory_uri()); ?>/assets/img/icons/clock.webp" alt="Icona orario"></span>
                            <span class="header-top-info__text"><?php echo esc_html('9.00 am - 19.00 pm'); ?></span>
                        </div>
                    </div>
                    <div class="col-lg-4">
                        <div class="header-top-info text-end">
                            <span class="header-top-info__image pr-1"><img width="19" height="19" src="<?php echo esc_url(get_template_directory_uri()); ?>/assets/img/icons/location.svg" alt="Icona posizione" style="filter: brightness(0) saturate(100%) invert(100%);"></span>
                            <span class="header-top-info__text"><?php echo esc_html('Via Campello, 57, 03011 Mole Bisleti FR'); ?></span>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <!-- menu bar - Centered Elegant Layout -->
        <div class="menu-bar">
            <div class="container">
                <div class="row">
                    <div class="col-lg-12 position-relative">
                        <div class="menu-bar-wrapper-centered">
                            <div class="centered-menu-layout">
                                <!-- Left Menu Items -->
                                <div class="menu-left">
                                    <nav class="main-nav-menu-left">
                                        <?php
                                        // Get menu items and split them
                                        $menu_items = wp_get_nav_menu_items(get_nav_menu_locations()['menu-1'] ?? '');
                                        if ($menu_items) {
                                            $total_items = count($menu_items);
                                            $half = ceil($total_items / 2);
                                            echo '<ul class="menu-items-left">';
                                            for ($i = 0; $i < $half; $i++) {
                                                if (isset($menu_items[$i])) {
                                                    $item = $menu_items[$i];
                                                    $current_class = (in_array('current-menu-item', $item->classes) || in_array('current_page_item', $item->classes)) ? ' current-menu-item' : '';
                                                    echo '<li class="menu-item' . $current_class . '"><a href="' . esc_url($item->url) . '">' . esc_html($item->title) . '</a></li>';
                                                }
                                            }
                                            echo '</ul>';
                                        }
                                        ?>
                                    </nav>
                                </div>

                                <!-- Center Logo -->
                                <div class="menu-center">
                                    <div class="brand-logo-centered">
                                        <a href="<?php echo esc_url(home_url('/')); ?>">
                                            <?php
                                            if (has_custom_logo()) {
                                                the_custom_logo();
                                            } else {
                                                echo '<img width="120" height="60" src="' . esc_url(get_template_directory_uri()) . '/assets/img/images/pacitto_movimento_terra.webp" class="img-fluid" alt="Logo">';
                                            }
                                            ?>
                                        </a>
                                    </div>
                                </div>

                                <!-- Right Menu Items + Contact Button -->
                                <div class="menu-right">
                                    <nav class="main-nav-menu-right">
                                        <?php
                                        if ($menu_items) {
                                            echo '<ul class="menu-items-right">';
                                            for ($i = $half; $i < $total_items; $i++) {
                                                if (isset($menu_items[$i])) {
                                                    $item = $menu_items[$i];
                                                    $current_class = (in_array('current-menu-item', $item->classes) || in_array('current_page_item', $item->classes)) ? ' current-menu-item' : '';
                                                    echo '<li class="menu-item' . $current_class . '"><a href="' . esc_url($item->url) . '">' . esc_html($item->title) . '</a></li>';
                                                }
                                            }
                                            echo '</ul>';
                                        }
                                        ?>
                                    </nav>
                                    <!-- call now button -->
                                    <div class="nav-call-button-centered">
                                        <a href="tel:0775408250" class="contact-btn-elegant">
                                            <span class="btn-icon"><i class="fas fa-phone"></i></span>
                                            <span class="btn-text">Contattaci</span>
                                        </a>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <!--====================  End of header area  ====================-->
    <!--====================  mobile header ====================-->
    <div class="mobile-header header-sticky bg-img space__inner--y20 background-repeat--x background-color--dark d-block d-lg-none" data-bg="<?php echo esc_url(get_template_directory_uri()); ?>/assets/img/icons/ruler.webp">
        <div class="container">
            <div class="row align-items-center">
                <div class="col-6">
                    <div class="brand-logo">
                        <a href="<?php echo esc_url(home_url('/')); ?>">
                            <?php
                            if (has_custom_logo()) {
                                // Aggiungi filtro per rendere il logo bianco nel menu mobile
                                echo '<div class="mobile-logo-wrapper" style="filter: brightness(0) saturate(100%) invert(100%); -webkit-filter: brightness(0) saturate(100%) invert(100%); drop-shadow(0px 0px 3px rgba(0, 0, 0, 0.5));">';
                                the_custom_logo();
                                echo '</div>';
                            } else {
                                echo '<img width="142" height="31" src="' . esc_url(get_template_directory_uri()) . '/assets/img/images/pacitto_movimento_terra.webp" class="img-fluid" alt="Logo Pacitto Movimento Terra" style="filter: brightness(0) saturate(100%) invert(100%); -webkit-filter: brightness(0) saturate(100%) invert(100%); drop-shadow(0px 0px 3px rgba(0, 0, 0, 0.5));">';
                            }
                            ?>
                        </a>
                    </div>
                </div>
                <div class="col-6">
                    <div class="mobile-menu-trigger-wrapper text-end" id="mobile-menu-trigger">
                        <span class="mobile-menu-trigger"></span>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <!--====================  End of mobile header  ====================-->
    <!--====================  offcanvas mobile menu ====================-->
    <div class="offcanvas-mobile-menu" id="mobile-menu-overlay">
        <a href="javascript:void(0)" class="offcanvas-menu-close" id="mobile-menu-close-trigger">
            <span class="menu-close"></span>
        </a>
        <div class="offcanvas-wrapper">
            <div class="offcanvas-inner-content">
                <div class="offcanvas-mobile-call-button">
                    <a href="tel:+390775435791" class="default-btn"><span class="call-icon"><img width="20" height="19" src="<?php echo esc_url(get_template_directory_uri()); ?>/assets/img/icons/phone.webp" alt="Icona telefono"></span><span>Chiama ora</span></a>
                </div>
                <nav class="offcanvas-navigation">
                    <?php
                    wp_nav_menu(
                        array(
                            'theme_location' => 'menu-1',
                            'menu_id'        => 'mobile-menu',
                            'container'      => false,
                            'menu_class'     => '',
                            'fallback_cb'    => false,
                        )
                    );
                    ?>
                </nav>
                <div class="offcanvas-widget-area">
                    <div class="off-canvas-contact-widget">
                        <div class="header-contact-info">
                            <ul class="header-contact-info__list">
                                <li><span class="header-top-info__image pr-1"><img width="20" height="19" src="<?php echo esc_url(get_template_directory_uri()); ?>/assets/img/icons/phone.webp" alt="" style="filter: brightness(0) saturate(100%);"></span> <?php echo esc_html('0775 408250'); ?></li>
                                <li><span class="header-top-info__image pr-1"><img width="19" height="19" src="<?php echo esc_url(get_template_directory_uri()); ?>/assets/img/icons/clock.webp" alt="" style="filter: brightness(0) saturate(100%);"></span> <?php echo esc_html('9.00 am - 19.00 pm'); ?></li>
                                <li><span class="header-top-info__image pr-1"><img width="19" height="19" src="<?php echo esc_url(get_template_directory_uri()); ?>/assets/img/icons/location.svg" alt="" style="filter: brightness(0) saturate(100%);"></span> <?php echo esc_html('Via Campello, 57'); ?><br><?php echo esc_html('03011 Mole Bisleti FR'); ?></li>
                            </ul>
                        </div>
                    </div>
                    <!--Off Canvas Widget Social Start-->

                    <!--Off Canvas Widget Social End-->
                </div>
            </div>
        </div>
    </div>
    <!--====================  End of offcanvas mobile menu  ====================-->
